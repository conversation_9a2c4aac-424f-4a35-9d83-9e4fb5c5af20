import 'package:stacked/stacked.dart';
import 'package:orange_ui/service/saved_profiles_service.dart';
import 'package:get/get.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/user_detail_screen/user_detail_screen.dart';

class HomeScreenViewModel extends BaseViewModel {
  bool isLoading = false;
  List<DatingProfile> profiles = [];
  int currentIndex = 0;

  final SavedProfilesService _savedProfilesService = SavedProfilesService();

  void init() {
    loadProfiles();
  }

  void loadProfiles() {
    isLoading = true;
    notifyListeners();

    // Simulate loading delay
    Future.delayed(const Duration(seconds: 1), () {
      profiles = [
        DatingProfile(
          id: 1,
          name: '<PERSON>',
          age: 28,
          imageUrls: [
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
            'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400',
          ],
          location: 'Los Angeles, CA',
          bio:
              'Yoga instructor and nature lover. Seeking mindful connections and outdoor adventures 🧘‍♀️🌿',
          interests: ['Yoga', 'Hiking', 'Meditation'],
          distance: '5 miles away',
        ),
        DatingProfile(
          id: 2,
          name: 'Jessica Brown',
          age: 24,
          imageUrls: [
            'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',
            'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400',
          ],
          location: 'Chicago, IL',
          bio:
              'Artist and coffee enthusiast. Let\'s create beautiful memories together! ☕🎨',
          interests: ['Art', 'Coffee', 'Museums'],
          distance: '3 miles away',
        ),
        DatingProfile(
          id: 3,
          name: 'Ashley Davis',
          age: 27,
          imageUrls: [
            'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400',
            'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400',
          ],
          location: 'Miami, FL',
          bio:
              'Beach lover and fitness enthusiast. Life\'s a beach, let\'s enjoy it together! 🏖️💪',
          interests: ['Fitness', 'Beach', 'Dancing'],
          distance: '1 mile away',
        ),
        DatingProfile(
          id: 4,
          name: 'Megan Taylor',
          age: 26,
          imageUrls: [
            'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400',
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
          ],
          location: 'Seattle, WA',
          bio:
              'Software engineer and book lover. Let\'s debug life together! 💻📚',
          interests: ['Technology', 'Reading', 'Gaming'],
          distance: '4 miles away',
        ),
        DatingProfile(
          id: 5,
          name: 'Rachel Green',
          age: 29,
          imageUrls: [
            'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400',
            'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',
          ],
          location: 'Austin, TX',
          bio:
              'Musician and food blogger. Good vibes and great food - that\'s my recipe for happiness! 🎵🍕',
          interests: ['Music', 'Food', 'Concerts'],
          distance: '6 miles away',
        ),
      ];

      isLoading = false;
      notifyListeners();
    });
  }

  void onSwipeLeft(int index) {
    // Handle pass/reject
    print('Passed on ${profiles[index].name}');
    _moveToNext();
  }

  void onSwipeRight(int index) {
    // Handle like
    print('Liked ${profiles[index].name}');
    _savedProfilesService.addProfile(profiles[index]);
    _moveToNext();
  }

  void onSwipeSuperlike(int index) {
    // Handle superlike
    print('Superliked ${profiles[index].name}');
    _savedProfilesService.addProfile(profiles[index]);
    _moveToNext();
  }

  void _moveToNext() {
    if (currentIndex < profiles.length - 1) {
      currentIndex++;
      notifyListeners();
    } else {
      // No more profiles, could load more here
      print('No more profiles');
    }
  }

  void onProfileTap(DatingProfile profile) {
    // Navigate to user detail screen with minimal userData
    print('Profile tapped: ${profile.name}');
    Get.to(() => UserDetailScreen());
  }

  void onNotificationTap() {
    // Handle notification tap
    print('Notifications tapped');
  }

  void onSearchTap() {
    // Handle search tap
    print('Search tapped');
  }
}

class DatingProfile {
  final int id;
  final String name;
  final int age;
  final List<String> imageUrls;
  final String location;
  final String bio;
  final List<String> interests;
  final String distance;

  DatingProfile({
    required this.id,
    required this.name,
    required this.age,
    required this.imageUrls,
    required this.location,
    required this.bio,
    required this.interests,
    required this.distance,
  });
}
