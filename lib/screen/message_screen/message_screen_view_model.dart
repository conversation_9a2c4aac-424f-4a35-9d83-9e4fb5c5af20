import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:orange_ui/api_provider/api_provider.dart';
import 'package:orange_ui/common/common_fun.dart';
import 'package:orange_ui/common/confirmation_dialog.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/chat_and_live_stream/chat.dart';
import 'package:orange_ui/model/setting.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/chat_screen/chat_screen.dart';

import 'package:orange_ui/screen/notification_screen/notification_screen.dart';
import 'package:orange_ui/screen/search_screen/search_screen.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/firebase_res.dart';
import 'package:stacked/stacked.dart';

class MessageScreenViewModel extends BaseViewModel {
  FirebaseFirestore db = FirebaseFirestore.instance;
  List<Conversation> userList = [];
  Conversation? conversation;
  bool isLoading = false;
  StreamSubscription<QuerySnapshot<Conversation>>? subscription;
  RegistrationUserData? userData;
  BannerAd? bannerAd;

  Appdata? settingAppData;

  void init() {
    getSettingData();
    loadDummyConversations(); // Load dummy data instead of Firebase
    getProfileApi();
  }

  void onNotificationTap() {
    CommonFun.isBloc(
      userData,
      onCompletion: () {
        Get.to(() => const NotificationScreen());
      },
    );
  }

  void getProfileApi() {
    ApiProvider().getProfile(userID: PrefService.userId).then((value) {
      userData = value?.data;
      notifyListeners();
    });
  }

  void onSearchTap() {
    CommonFun.isBloc(
      userData,
      onCompletion: () {
        Get.to(() => const SearchScreen());
      },
    );
  }

  void onUserTap(Conversation conversation) {
    CommonFun.isBloc(
      userData,
      onCompletion: () {
        Get.to(() => ChatScreen(conversation: conversation));
      },
    );
  }

  void getChatUsers() {
    isLoading = true;
    PrefService.getUserData().then((value) {
      subscription = db
          .collection(FirebaseRes.userChatList)
          .doc('${value?.id}')
          .collection(FirebaseRes.userList)
          .orderBy(FirebaseRes.time, descending: true)
          .withConverter(
            fromFirestore: Conversation.fromFirestore,
            toFirestore: (Conversation value, options) => value.toFirestore(),
          )
          .snapshots()
          .listen((element) {
            userList = [];
            for (int i = 0; i < element.docs.length; i++) {
              if (element.docs[i].data().isDeleted == false) {
                userList.add(element.docs[i].data());
                notifyListeners();
              }
            }
            isLoading = false;
            notifyListeners();
          });
    });
  }

  void getBannerAd() {
    CommonFun.bannerAd(
      (ad) {
        bannerAd = ad as BannerAd;
        notifyListeners();
      },
      bannerId:
          Platform.isIOS
              ? settingAppData?.admobBannerIos
              : settingAppData?.admobBanner,
    );
  }

  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  }

  void onLongPress(Conversation? conversation) {
    HapticFeedback.vibrate();
    Get.dialog(
      ConfirmationDialog(
        onTap: () {
          db
              .collection(FirebaseRes.userChatList)
              .doc(userData?.identity)
              .collection(FirebaseRes.userList)
              .doc(conversation?.user?.userIdentity)
              .update({
                FirebaseRes.isDeleted: true,
                FirebaseRes.deletedId:
                    '${DateTime.now().millisecondsSinceEpoch}',
                FirebaseRes.block: false,
                FirebaseRes.blockFromOther: false,
              })
              .then((value) {
                Get.back();
              });
        },
        description: S.current.messageWillOnlyBeRemoved,
        dialogSize: 1.9,
        padding: const EdgeInsets.symmetric(horizontal: 40),
      ),
    );
    notifyListeners();
  }

  void getSettingData() {
    PrefService.getSettingData().then((value) {
      settingAppData = value?.appdata;
      getBannerAd();
      notifyListeners();
    });
  }

  void loadDummyConversations() {
    isLoading = true;
    notifyListeners();

    // Simulate loading delay
    Future.delayed(const Duration(seconds: 1), () {
      userList = [
        Conversation(
          user: ChatUser(
            userid: 1,
            username: 'Sarah Johnson',
            userIdentity: '<EMAIL>',
            image:
                'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
            city: 'Los Angeles, CA',
            age: '28',
            isHost: false,
            isNewMsg: true,
            date:
                DateTime.now()
                    .subtract(const Duration(hours: 2))
                    .millisecondsSinceEpoch
                    .toDouble(),
          ),
          lastMsg: 'Thanks for the coffee recommendation! ☕',
          time:
              DateTime.now()
                  .subtract(const Duration(hours: 2))
                  .millisecondsSinceEpoch
                  .toDouble(),
          isDeleted: false,
          block: false,
          blockFromOther: false,
          deletedId: '',
          newMsg: '0',
        ),
        Conversation(
          user: ChatUser(
            userid: 2,
            username: 'Jessica Brown',
            userIdentity: '<EMAIL>',
            image:
                'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
            city: 'Chicago, IL',
            age: '24',
            isHost: false,
            isNewMsg: false,
            date:
                DateTime.now()
                    .subtract(const Duration(days: 1))
                    .millisecondsSinceEpoch
                    .toDouble(),
          ),
          lastMsg: 'That sounds like a great plan!',
          time:
              DateTime.now()
                  .subtract(const Duration(days: 1))
                  .millisecondsSinceEpoch
                  .toDouble(),
          isDeleted: false,
          block: false,
          blockFromOther: false,
          deletedId: '',
          newMsg: '1',
        ),
        Conversation(
          user: ChatUser(
            userid: 3,
            username: 'Ashley Davis',
            userIdentity: '<EMAIL>',
            image:
                'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150',
            city: 'Miami, FL',
            age: '26',
            isHost: false,
            isNewMsg: false,
            date:
                DateTime.now()
                    .subtract(const Duration(days: 2))
                    .millisecondsSinceEpoch
                    .toDouble(),
          ),
          lastMsg: 'See you tomorrow! 👋',
          time:
              DateTime.now()
                  .subtract(const Duration(days: 2))
                  .millisecondsSinceEpoch
                  .toDouble(),
          isDeleted: false,
          block: false,
          blockFromOther: false,
          deletedId: '',
          newMsg: '0',
        ),
        Conversation(
          user: ChatUser(
            userid: 4,
            username: 'Maria Garcia',
            userIdentity: '<EMAIL>',
            image:
                'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150',
            city: 'Austin, TX',
            age: '27',
            isHost: false,
            isNewMsg: false,
            date:
                DateTime.now()
                    .subtract(const Duration(days: 3))
                    .millisecondsSinceEpoch
                    .toDouble(),
          ),
          lastMsg: 'The concert was amazing! 🎵',
          time:
              DateTime.now()
                  .subtract(const Duration(days: 3))
                  .millisecondsSinceEpoch
                  .toDouble(),
          isDeleted: false,
          block: false,
          blockFromOther: false,
          deletedId: '',
          newMsg: '0',
        ),
      ];

      isLoading = false;
      notifyListeners();
    });
  }
}
